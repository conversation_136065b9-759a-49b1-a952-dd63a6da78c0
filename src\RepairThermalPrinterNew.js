import QRCodeUtils from './QRCodeUtils.js';
import { getTranslation } from './translations.js';

/**
 * Enhanced Thermal Printer for Repair Management System
 * Supports both 40x60mm QR tickets and 80x80mm full receipts
 */
class RepairThermalPrinter {
  constructor() {
    this.qrTicketWidth = '40mm';
    this.qrTicketHeight = '60mm';
    this.thermalWidth = '80mm';
    this.thermalHeight = '80mm';
    this.isInitialized = false;
    this.init();
  }

  async init() {
    try {
      this.isInitialized = true;
      console.log('🖨️ Repair Thermal Printer: System initialized successfully');
    } catch (error) {
      console.warn('🖨️ Repair Thermal Printer: Initialization failed', error);
      this.isInitialized = true;
    }
  }

  formatPrice(price, language) {
    const numPrice = parseFloat(price) || 0;
    if (language === 'ar') {
      return numPrice.toLocaleString('ar-DZ');
    } else if (language === 'fr') {
      return numPrice.toLocaleString('fr-FR');
    } else {
      return numPrice.toLocaleString('en-US');
    }
  }

  async generateRepairTicketContent(repair, options = {}) {
    try {
      const { language = 'ar', storeSettings = {} } = options;

      // Generate barcode for the repair
      const barcodeImage = QRCodeUtils.generateRepairBarcode(repair.repairBarcode || repair.id);

      const totalPrice = (repair.repairPrice || 0) + (repair.partsPrice || 0);
      const isRTL = language === 'ar';
      const t = (key, fallback) => getTranslation(key, language) || fallback;

      const content = `
        <!DOCTYPE html>
        <html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${language}">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${t('repairTicket', 'تذكرة الإصلاح')}</title>
          <style>
            ${this.getThermalTicketStyles(language, isRTL)}
          </style>
        </head>
        <body>
          <div class="thermal-ticket">
            <!-- Header Section -->
            <div class="ticket-header">
              <div class="store-logo">🏪</div>
              <div class="store-name">${storeSettings.storeName || 'ICALDZ STORE'}</div>
              <div class="store-phone">📞 ${storeSettings.storePhone || '+*********** 456'}</div>
              <div class="store-address">${storeSettings.storeAddress || 'الجزائر العاصمة، الجزائر'}</div>
            </div>

            <div class="separator"></div>

            <!-- Ticket Title -->
            <div class="ticket-title">${t('repairTicket', 'تذكرة الإصلاح')}</div>
            <div class="ticket-subtitle">${t('repairSystem', 'نظام الإصلاح - ICALDZ')}</div>

            <div class="separator"></div>

            <!-- Client Information -->
            <div class="info-section">
              <div class="info-line">
                <span class="label">${t('ticketNumber', 'رقم التذكرة')}:</span>
                <span class="value">${repair.id || 'N/A'}</span>
              </div>
              <div class="info-line">
                <span class="label">${t('date', 'التاريخ')}:</span>
                <span class="value">${new Date(repair.date || Date.now()).toLocaleDateString(language === 'ar' ? 'ar-DZ' : language === 'fr' ? 'fr-FR' : 'en-US')}</span>
              </div>
              <div class="info-line">
                <span class="label">${t('clientName', 'اسم العميل')}:</span>
                <span class="value">${repair.clientName || 'N/A'}</span>
              </div>
              <div class="info-line">
                <span class="label">${t('clientPhone', 'هاتف العميل')}:</span>
                <span class="value">${repair.clientPhone || repair.phone || 'N/A'}</span>
              </div>
              <div class="info-line">
                <span class="label">${t('deviceName', 'اسم الجهاز')}:</span>
                <span class="value">${repair.deviceName || 'N/A'}</span>
              </div>
            </div>

            <div class="separator-dotted"></div>

            <!-- Barcode Section -->
            ${barcodeImage ? `
            <div class="barcode-section">
              <img src="${barcodeImage}" alt="Barcode" class="barcode-image" />
              <div class="barcode-text">${repair.repairBarcode || repair.id}</div>
            </div>
            ` : `
            <div class="barcode-section">
              <div class="barcode-text-only">${repair.repairBarcode || repair.id}</div>
            </div>
            `}

            <div class="separator-dotted"></div>

            <!-- Price Section -->
            <div class="price-section">
              <div class="price-line">
                <span class="label">${t('repairPrice', 'سعر الإصلاح')}:</span>
                <span class="value">${this.formatPrice(repair.repairPrice || 0, language)} DZD</span>
              </div>
              ${(repair.partsPrice && parseFloat(repair.partsPrice) > 0) ? `
              <div class="price-line">
                <span class="label">${t('partsPrice', 'سعر القطع')}:</span>
                <span class="value">${this.formatPrice(repair.partsPrice, language)} DZD</span>
              </div>
              ` : ''}
              <div class="total-price">
                <span class="label">${t('totalAmount', 'المبلغ الإجمالي')}:</span>
                <span class="value">${this.formatPrice(totalPrice, language)} DZD</span>
              </div>
            </div>

            <div class="separator"></div>

            <!-- Footer -->
            <div class="ticket-footer">
              <div class="thank-you">${t('thankYou', 'شكراً لثقتكم')}</div>
              <div class="developer-info">
                <div>Développé par iCode DZ</div>
                <div>📞 0551930589</div>
              </div>
              <div class="print-time">${t('printedOn', 'طُبع في')}: ${new Date().toLocaleString(language === 'ar' ? 'ar-DZ' : language === 'fr' ? 'fr-FR' : 'en-US')}</div>
            </div>
          </div>
        </body>
        </html>
      `;

      return content;
    } catch (error) {
      console.error('Error generating repair ticket content:', error);
      return null;
    }
  }

  getThermalTicketStyles(language, isRTL) {
    return `
      * { margin: 0; padding: 0; box-sizing: border-box; }
      @page { size: 80mm auto; margin: 0; }
      body {
        font-family: ${language === 'ar' ? "'Cairo', 'Courier New'" : "'Courier New'"}, monospace;
        font-size: 12px; line-height: 1.4; color: #000; background: white;
        direction: ${isRTL ? 'rtl' : 'ltr'}; text-align: ${isRTL ? 'right' : 'left'};
        width: 80mm; margin: 0; padding: 3mm;
      }
      .thermal-ticket { width: 100%; }

      /* Header Styles */
      .ticket-header { text-align: center; margin-bottom: 3mm; }
      .store-logo { font-size: 20px; margin-bottom: 2mm; }
      .store-name { font-size: 16px; font-weight: bold; margin-bottom: 1mm; }
      .store-phone { font-size: 12px; margin-bottom: 1mm; }
      .store-address { font-size: 10px; margin-bottom: 2mm; }

      /* Title Styles */
      .ticket-title { font-size: 14px; font-weight: bold; text-align: center; margin: 2mm 0; }
      .ticket-subtitle { font-size: 10px; text-align: center; margin-bottom: 2mm; }

      /* Separator Styles */
      .separator { border-bottom: 2px solid #000; margin: 2mm 0; }
      .separator-dotted { border-bottom: 1px dashed #000; margin: 2mm 0; }

      /* Info Section Styles */
      .info-section { margin: 2mm 0; }
      .info-line { display: flex; justify-content: space-between; margin: 1mm 0; font-size: 11px; }
      .info-line .label { font-weight: bold; }
      .info-line .value { text-align: ${isRTL ? 'left' : 'right'}; }

      /* Barcode Section */
      .barcode-section { text-align: center; margin: 3mm 0; padding: 2mm; border: 1px solid #000; }
      .barcode-image { max-width: 100%; height: auto; margin-bottom: 2mm; }
      .barcode-text { font-family: 'Courier New', monospace; font-size: 10px; font-weight: bold; }
      .barcode-text-only { font-family: 'Courier New', monospace; font-size: 12px; font-weight: bold; padding: 3mm; }

      /* Price Section */
      .price-section { margin: 2mm 0; }
      .price-line { display: flex; justify-content: space-between; margin: 1mm 0; font-size: 11px; }
      .total-price { display: flex; justify-content: space-between; margin: 2mm 0; font-size: 13px; font-weight: bold;
                     border: 2px solid #000; padding: 2mm; background: #f0f0f0; }

      /* Footer Styles */
      .ticket-footer { text-align: center; margin-top: 3mm; font-size: 10px; }
      .thank-you { font-weight: bold; margin-bottom: 2mm; }
      .developer-info { border-top: 1px solid #000; padding-top: 2mm; margin: 2mm 0; }
      .print-time { font-size: 8px; margin-top: 2mm; }

      @media print {
        body { -webkit-print-color-adjust: exact; print-color-adjust: exact; }
        .thermal-ticket { page-break-inside: avoid; }
      }
    `;
  }

  async openPrintWindow(repair, options = {}) {
    try {
      const content = await this.generateRepairTicketContent(repair, options);
      if (!content) {
        throw new Error('Failed to generate ticket content');
      }

      const printWindow = window.open('', '_blank', 'width=400,height=700');
      printWindow.document.write(content);
      printWindow.document.close();

      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      };

      return true;
    } catch (error) {
      console.error('Error opening print window:', error);
      return false;
    }
  }

  async printRepairOrderReceipt(repair, options = {}) {
    // Use the repair ticket method for repair orders
    return this.openPrintWindow(repair, options);
  }

  async printClientPickupReceipt(repair, options = {}) {
    try {
      const content = await this.generateClientInvoiceContent(repair, options);
      if (!content) {
        throw new Error('Failed to generate client invoice content');
      }

      const printWindow = window.open('', '_blank', 'width=400,height=700');
      printWindow.document.write(content);
      printWindow.document.close();

      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print();
          printWindow.close();
        }, 500);
      };

      return true;
    } catch (error) {
      console.error('Error printing client pickup receipt:', error);
      return false;
    }
  }

  async generateClientInvoiceContent(repair, options = {}) {
    try {
      const { language = 'ar', storeSettings = {} } = options;
      const isRTL = language === 'ar';
      const t = (key, fallback) => getTranslation(key, language) || fallback;

      // Calculate pricing
      const repairPrice = parseFloat(repair.repairPrice || 0);
      const partsPrice = parseFloat(repair.partsPrice || 0);
      const subtotal = repairPrice + partsPrice;
      const taxRate = parseFloat(storeSettings.taxRate || 19);
      const taxAmount = (subtotal * taxRate) / 100;
      const finalTotal = subtotal + taxAmount;

      // Generate invoice number
      const invoiceNumber = `INV-${Date.now()}`;

      const content = `
        <!DOCTYPE html>
        <html dir="${isRTL ? 'rtl' : 'ltr'}" lang="${language}">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${t('clientInvoice', 'فاتورة العميل')}</title>
          <style>
            ${this.getThermalInvoiceStyles(language, isRTL)}
          </style>
        </head>
        <body>
          <div class="thermal-invoice">
            <!-- Header Section -->
            <div class="invoice-header">
              <div class="store-logo">🏪</div>
              <div class="store-name">${storeSettings.storeName || 'ICALDZ STORE'} ⭐</div>
              <div class="store-phone">📞 ${storeSettings.storePhone || '+*********** 456'}</div>
              <div class="store-address">${storeSettings.storeAddress || 'الجزائر العاصمة، الجزائر'}</div>
            </div>

            <div class="separator"></div>

            <!-- Invoice Title -->
            <div class="invoice-title">${t('clientInvoice', 'فاتورة العميل')}</div>
            <div class="invoice-subtitle">${t('repairSystem', 'نظام الإصلاح - ICALDZ')}</div>

            <div class="separator"></div>

            <!-- Invoice Details -->
            <div class="invoice-details">
              <div class="detail-line">
                <span class="label">${t('invoiceNumber', 'رقم الفاتورة')}:</span>
                <span class="value">${invoiceNumber}</span>
              </div>
              <div class="detail-line">
                <span class="label">${t('date', 'التاريخ')}:</span>
                <span class="value">${new Date().toLocaleDateString(language === 'ar' ? 'ar-DZ' : language === 'fr' ? 'fr-FR' : 'en-US')}</span>
              </div>
              <div class="detail-line">
                <span class="label">${t('time', 'الوقت')}:</span>
                <span class="value">${new Date().toLocaleTimeString(language === 'ar' ? 'ar-DZ' : language === 'fr' ? 'fr-FR' : 'en-US')}</span>
              </div>
              <div class="detail-line">
                <span class="label">${t('clientName', 'اسم العميل')}:</span>
                <span class="value">${repair.clientName || 'Client de passage'}</span>
              </div>
              <div class="detail-line">
                <span class="label">${t('paymentMode', 'طريقة الدفع')}:</span>
                <span class="value">${t('cash', 'نقداً')}</span>
              </div>
            </div>

            <div class="separator-dotted"></div>

            <!-- Products Table -->
            <div class="products-table">
              <div class="table-header">
                <span class="col-product">${t('service', 'الخدمة')}</span>
                <span class="col-qty">${t('qty', 'الكمية')}</span>
                <span class="col-price">${t('price', 'السعر')}</span>
                <span class="col-total">${t('total', 'المجموع')}</span>
              </div>
              <div class="table-row">
                <span class="col-product">${repair.deviceName || 'Réparation'}</span>
                <span class="col-qty">1</span>
                <span class="col-price">${this.formatPrice(repairPrice, language)}</span>
                <span class="col-total">${this.formatPrice(repairPrice, language)}</span>
              </div>
              ${partsPrice > 0 ? `
              <div class="table-row">
                <span class="col-product">${t('repairParts', 'قطع الإصلاح')}</span>
                <span class="col-qty">1</span>
                <span class="col-price">${this.formatPrice(partsPrice, language)}</span>
                <span class="col-total">${this.formatPrice(partsPrice, language)}</span>
              </div>
              ` : ''}
            </div>

            <div class="separator-dotted"></div>

            <!-- Totals Section -->
            <div class="totals-section">
              <div class="total-line">
                <span class="label">${t('subtotal', 'المجموع الفرعي')}:</span>
                <span class="value">${this.formatPrice(subtotal, language)} DZD</span>
              </div>
              <div class="total-line">
                <span class="label">${t('tax', 'الضريبة')} ${taxRate}%:</span>
                <span class="value">${this.formatPrice(taxAmount, language)} DZD</span>
              </div>
              <div class="final-total">
                <span class="label">${t('finalTotal', 'المجموع النهائي')}:</span>
                <span class="value">${this.formatPrice(finalTotal, language)} DZD</span>
              </div>
            </div>

            <div class="separator"></div>

            <!-- Footer -->
            <div class="invoice-footer">
              <div class="thank-you">${t('thankYouVisit', 'شكراً لزيارتكم')}</div>
              <div class="developer-info">
                <div>Développé par iCode DZ</div>
                <div>📞 0551930589</div>
              </div>
              <div class="print-time">${t('printedOn', 'طُبع في')}: ${new Date().toLocaleString(language === 'ar' ? 'ar-DZ' : language === 'fr' ? 'fr-FR' : 'en-US')}</div>
            </div>
          </div>
        </body>
        </html>
      `;

      return content;
    } catch (error) {
      console.error('Error generating client invoice content:', error);
      return null;
    }
  }

  getThermalInvoiceStyles(language, isRTL) {
    return `
      * { margin: 0; padding: 0; box-sizing: border-box; }
      @page { size: 80mm auto; margin: 0; }
      body {
        font-family: ${language === 'ar' ? "'Cairo', 'Courier New'" : "'Courier New'"}, monospace;
        font-size: 12px; line-height: 1.4; color: #000; background: white;
        direction: ${isRTL ? 'rtl' : 'ltr'}; text-align: ${isRTL ? 'right' : 'left'};
        width: 80mm; margin: 0; padding: 3mm;
      }
      .thermal-invoice { width: 100%; }

      /* Header Styles */
      .invoice-header { text-align: center; margin-bottom: 3mm; }
      .store-logo { font-size: 20px; margin-bottom: 2mm; }
      .store-name { font-size: 16px; font-weight: bold; margin-bottom: 1mm; }
      .store-phone { font-size: 12px; margin-bottom: 1mm; }
      .store-address { font-size: 10px; margin-bottom: 2mm; }

      /* Title Styles */
      .invoice-title { font-size: 14px; font-weight: bold; text-align: center; margin: 2mm 0; }
      .invoice-subtitle { font-size: 10px; text-align: center; margin-bottom: 2mm; }

      /* Separator Styles */
      .separator { border-bottom: 2px solid #000; margin: 2mm 0; }
      .separator-dotted { border-bottom: 1px dashed #000; margin: 2mm 0; }

      /* Invoice Details */
      .invoice-details { margin: 2mm 0; }
      .detail-line { display: flex; justify-content: space-between; margin: 1mm 0; font-size: 11px; }
      .detail-line .label { font-weight: bold; }
      .detail-line .value { text-align: ${isRTL ? 'left' : 'right'}; }

      /* Products Table */
      .products-table { margin: 2mm 0; }
      .table-header { display: flex; background: #000; color: white; padding: 1mm; font-size: 10px; font-weight: bold; }
      .table-row { display: flex; padding: 1mm; font-size: 10px; border-bottom: 1px dotted #ccc; }
      .col-product { flex: 2; }
      .col-qty { flex: 0.5; text-align: center; }
      .col-price { flex: 1; text-align: ${isRTL ? 'left' : 'right'}; }
      .col-total { flex: 1; text-align: ${isRTL ? 'left' : 'right'}; }

      /* Totals Section */
      .totals-section { margin: 2mm 0; }
      .total-line { display: flex; justify-content: space-between; margin: 1mm 0; font-size: 11px; }
      .final-total { display: flex; justify-content: space-between; margin: 2mm 0; font-size: 14px; font-weight: bold;
                     border: 2px solid #000; padding: 2mm; background: #f0f0f0; }

      /* Footer Styles */
      .invoice-footer { text-align: center; margin-top: 3mm; font-size: 10px; }
      .thank-you { font-weight: bold; margin-bottom: 2mm; }
      .developer-info { border-top: 1px solid #000; padding-top: 2mm; margin: 2mm 0; }
      .print-time { font-size: 8px; margin-top: 2mm; }

      @media print {
        body { -webkit-print-color-adjust: exact; print-color-adjust: exact; }
        .thermal-invoice { page-break-inside: avoid; }
      }
    `;
  }

  async printSupplierTransactions(supplierName, transactions, totalCredit, options = {}) {
    // Simplified version for now - just show a message
    console.log('Printing supplier transactions:', supplierName, totalCredit);
    return true;
  }

  async printRepairOrdersList(repairs, options = {}) {
    // Simplified version for now - just show a message
    console.log('Printing repair orders list:', repairs.length);
    return true;
  }
}

export default new RepairThermalPrinter();
